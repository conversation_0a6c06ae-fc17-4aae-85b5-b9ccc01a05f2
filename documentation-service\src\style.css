* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

.documentation-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e1e5e9;
  padding: 20px 0;
  overflow-y: auto;
  position: fixed;
  height: 100vh;
  z-index: 100;
}

.sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e1e5e9;
  margin-bottom: 20px;
}

.sidebar-header h1 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 5px;
}

.sidebar-header p {
  color: #6c757d;
  font-size: 0.9rem;
}

.nav-section {
  margin-bottom: 20px;
}

.nav-section-title {
  padding: 0 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 10px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 20px;
  color: #495057;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.nav-icon {
  font-size: 1rem;
}

.nav-link:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.nav-link.active {
  background-color: #007bff;
  color: white;
  border-right: 3px solid #0056b3;
}

/* Content Styles */
.content {
  flex: 1;
  margin-left: 280px;
  padding: 40px;
  max-width: calc(100% - 280px);
}

.content-header {
  margin-bottom: 30px;
}

.content-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.content-description {
  font-size: 1.1rem;
  color: #6c757d;
  line-height: 1.7;
}

.endpoint-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.endpoint-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.method-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.method-get { background: #d4edda; color: #155724; }
.method-post { background: #d1ecf1; color: #0c5460; }
.method-put { background: #fff3cd; color: #856404; }
.method-delete { background: #f8d7da; color: #721c24; }

.endpoint-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f8f9fa;
  padding: 10px 15px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
  margin: 15px 0;
  font-size: 0.9rem;
}

.code-block {
  position: relative;
  margin: 20px 0;
}

.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  z-index: 10;
}

.copy-btn:hover {
  background: #0056b3;
}

pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 20px;
  overflow-x: auto;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.validation-rules {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.validation-rules h4 {
  color: #856404;
  margin-bottom: 10px;
}

.validation-rules ul {
  margin-left: 20px;
}

.validation-rules li {
  margin-bottom: 5px;
  color: #856404;
}

.info-box {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.info-box h4 {
  color: #0c5460;
  margin-bottom: 10px;
}

.warning-box {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.warning-box h4 {
  color: #721c24;
  margin-bottom: 10px;
}

/* Additional Styles */
.endpoint-section h4 {
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.endpoint-section ol {
  margin-left: 20px;
  margin-bottom: 15px;
}

.endpoint-section ol li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.endpoint-section ul {
  margin-left: 20px;
  margin-bottom: 15px;
}

.endpoint-section ul li {
  margin-bottom: 5px;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: relative;
    height: auto;
  }

  .content {
    margin-left: 0;
    max-width: 100%;
    padding: 20px;
  }

  .documentation-container {
    flex-direction: column;
  }

  .endpoint-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .method-badge {
    align-self: flex-start;
  }
}
