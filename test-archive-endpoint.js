const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

async function testArchiveEndpoint() {
  try {
    console.log('Testing Archive Service health endpoint...');

    const healthResponse = await axios.get('http://localhost:3002/health');
    console.log('✓ Health check:', healthResponse.status, healthResponse.data);

    const testJobId = uuidv4();
    const testUserId = uuidv4();

    console.log('\nTesting Archive Service /archive/jobs endpoint...');

    const archiveResponse = await axios.post('http://localhost:3002/archive/jobs', {
      job_id: testJobId,
      user_id: testUserId,
      assessment_data: {
        riasec: { realistic: 75, investigative: 85 },
        ocean: { conscientiousness: 65, extraversion: 55 }
      },
      assessment_name: 'Test Assessment',
      status: 'queued'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Internal-Service': 'true',
        'X-Service-Key': process.env.INTERNAL_SERVICE_KEY || 'f8c1af59d85da6581036e18b4b9e0ec35d1fdefe1a93837d5b4746c9984ea4c1'
      },
      timeout: 5000
    });

    console.log('✓ Archive endpoint success:', archiveResponse.status, archiveResponse.data);

    console.log('\nTesting Archive Service /jobs endpoint...');

    const response = await axios.post('http://localhost:3002/jobs', {
      job_id: uuidv4(),
      user_id: uuidv4(),
      job_id: 'test-job-123',
      user_id: 'test-user-456',
      assessment_data: {
        riasec: { realistic: 75, investigative: 85 },
        ocean: { conscientiousness: 65, extraversion: 55 }
      },
      assessment_name: 'Test Assessment',
      status: 'queued'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-Internal-Service': 'true',
        'X-Service-Key': process.env.INTERNAL_SERVICE_KEY || 'f8c1af59d85da6581036e18b4b9e0ec35d1fdefe1a93837d5b4746c9984ea4c1'
      },
      timeout: 5000
    });
    
    console.log('✓ Success:', response.status, response.data);
  } catch (error) {
    console.log('✗ Error:', error.response?.status, error.response?.data || error.message);
    console.log('Request headers:', error.config?.headers);
    console.log('Request URL:', error.config?.url);
  }
}

testArchiveEndpoint();
