# =================================
# ATMA Testing Service Configuration
# =================================

# Environment Configuration
NODE_ENV=testing

# API Configuration
API_BASE_URL=http://localhost:3000
WEBSOCKET_URL=http://localhost:3005

# Test Configuration
TEST_USER_COUNT=100
TEST_CONCURRENCY=100

# Test User Configuration
TEST_EMAIL_PREFIX=testuser
TEST_EMAIL_DOMAIN=example.com
TEST_PASSWORD=TestPassword123!

# Load Testing Configuration
LOAD_TEST_DURATION=300000
LOAD_TEST_RAMP_UP=30000
LOAD_TEST_REQUESTS_PER_SECOND=10

# Performance Testing Configuration
PERFORMANCE_TEST_ENABLED=true
PERFORMANCE_THRESHOLD_MS=5000
PERFORMANCE_MEMORY_THRESHOLD_MB=512

# Database Configuration (for test data cleanup)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=password_goes_here

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/testing-service.log
ENABLE_VERBOSE_LOGGING=false

# Test Data Configuration
ENABLE_TEST_DATA_CLEANUP=true
CLEANUP_AFTER_TESTS=true
PRESERVE_TEST_ACCOUNTS=false

# WebSocket Testing Configuration
WEBSOCKET_CONNECTION_TIMEOUT=5000
WEBSOCKET_MESSAGE_TIMEOUT=10000
WEBSOCKET_MAX_CONNECTIONS=1000

# Stress Testing Configuration
STRESS_TEST_ENABLED=false
STRESS_TEST_MAX_USERS=1000
STRESS_TEST_DURATION=600000

# Monitoring Configuration
ENABLE_METRICS_COLLECTION=true
METRICS_INTERVAL=5000
ENABLE_REAL_TIME_MONITORING=true

# Error Handling Configuration
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY=1000
FAIL_FAST_ON_CRITICAL_ERROR=true

# Test Report Configuration
GENERATE_HTML_REPORT=true
GENERATE_JSON_REPORT=true
REPORT_OUTPUT_DIR=./test-reports

# Development Configuration
ENABLE_DEBUG_MODE=false
ENABLE_TEST_ISOLATION=true
PARALLEL_TEST_EXECUTION=true
