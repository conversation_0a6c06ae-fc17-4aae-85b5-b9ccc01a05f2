{"name": "atma-e2e-load-testing", "version": "1.0.0", "description": "E2E and Load Testing for ATMA Backend", "main": "index.js", "scripts": {"test:e2e": "node e2e-test.js", "test:load": "node load-test.js", "test:all": "npm run test:e2e && npm run test:load", "test:websocket": "node test-websocket-routing.js", "test:chatbot:proxy": "node chatbot/test-chatbot-proxy.js", "test:chatbot:simple": "node chatbot/test-chatbot-simple.js", "test:chatbot:openrouter": "node chatbot/test-openrouter-integration.js", "test:chatbot:ai": "node chatbot/test-openrouter-ai-integration.js", "test:chatbot:direct": "node chatbot/test-openrouter-direct.js", "test:chatbot:report": "node chatbot/test-openrouter-final-report.js", "test:chatbot:all": "npm run test:chatbot:proxy && npm run test:chatbot:simple && npm run test:chatbot:openrouter", "test:phase3": "node test-phase3-assessment-integration.js", "test:phase3:full": "npm run test:e2e && npm run test:phase3", "cleanup:single": "node cleanup-account.js", "cleanup:batch": "node batch-cleanup.js"}, "dependencies": {"axios": "^1.6.0", "socket.io-client": "^4.7.2", "chalk": "^4.1.2", "cli-progress": "^3.12.0", "uuid": "^9.0.0", "colors": "^1.4.0"}, "devDependencies": {}, "keywords": ["testing", "e2e", "load-testing", "atma"], "author": "ATMA Team", "license": "MIT"}