#!/usr/bin/env node

const axios = require('axios');
const chalk = require('chalk');
const config = require('./config');
const TestUtils = require('./utils');

/**
 * Phase 3 Assessment Integration Test
 * Tests assessment-to-chatbot integration features
 */
class Phase3AssessmentIntegrationTest {
  constructor() {
    this.testUser = null;
    this.startTime = Date.now();
    this.testResults = {
      assessmentReadiness: false,
      autoInitialization: false,
      welcomeMessage: false,
      suggestedQuestions: false,
      assessmentConversation: false,
      contextualResponses: false
    };
  }

  async run() {
    console.log(chalk.bold.blue('\n🧪 Phase 3: Assessment Integration Test Started'));
    console.log(chalk.gray('Testing assessment-to-chatbot integration features\n'));

    try {
      await this.setupTestUser();
      await this.testAssessmentReadiness();
      await this.testAutoInitialization();
      await this.testSuggestedQuestions();
      await this.testAssessmentConversation();
      await this.testContextualResponses();
      await this.cleanup();
      
      this.generateReport();
    } catch (error) {
      TestUtils.logError(`Phase 3 test failed: ${error.message}`);
      console.error(error);
      process.exit(1);
    }
  }

  async setupTestUser() {
    TestUtils.logStage('Setup: Creating Test User');
    
    try {
      // Create test user
      const userData = TestUtils.generateRandomUser(1);
      this.testUser = userData;
      
      // Register user
      const registerResponse = await axios.post(`${config.api.baseUrl}/api/auth/register`, {
        email: userData.email,
        password: userData.password
      });

      if (registerResponse.data.success) {
        this.testUser.id = registerResponse.data.data?.user?.id;
        TestUtils.logSuccess('Test user registered');
      }

      // Login user
      const loginResponse = await axios.post(`${config.api.baseUrl}/api/auth/login`, {
        email: userData.email,
        password: userData.password
      });

      if (loginResponse.data.success) {
        this.testUser.token = loginResponse.data.data.token;
        TestUtils.logSuccess('Test user logged in');
      }

      await TestUtils.delay(1000);
    } catch (error) {
      throw new Error(`Setup failed: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async testAssessmentReadiness() {
    TestUtils.logStage('Test 1: Assessment Readiness Check');
    
    try {
      const response = await axios.get(
        `${config.api.baseUrl}/api/chatbot/assessment-ready/${this.testUser.id}`,
        {
          headers: {
            'Authorization': `Bearer ${this.testUser.token}`
          },
          timeout: config.api.timeout
        }
      );

      if (response.status === 200) {
        this.testResults.assessmentReadiness = true;
        TestUtils.logSuccess('Assessment readiness endpoint working');
        TestUtils.logInfo(`Has assessment: ${response.data.has_assessment}`);
        TestUtils.logInfo(`Ready for chatbot: ${response.data.ready_for_chatbot}`);
        
        if (response.data.has_assessment) {
          this.testUser.assessmentId = response.data.assessment_id;
        }
      }

      await TestUtils.delay(1000);
    } catch (error) {
      TestUtils.logError(`Assessment readiness test failed: ${error.response?.data?.error || error.message}`);
    }
  }

  async testAutoInitialization() {
    TestUtils.logStage('Test 2: Auto-Initialize Assessment Conversation');
    
    try {
      const response = await axios.post(
        `${config.api.baseUrl}/api/chatbot/assessment/auto-initialize`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${this.testUser.token}`,
            'Content-Type': 'application/json'
          },
          timeout: config.api.timeout
        }
      );

      if (response.status === 201 || response.status === 200) {
        this.testResults.autoInitialization = true;
        TestUtils.logSuccess('Auto-initialization working');
        
        if (response.data.conversation) {
          this.testUser.conversationId = response.data.conversation.id;
          TestUtils.logInfo(`Conversation created: ${response.data.conversation.id}`);
          TestUtils.logInfo(`Context type: ${response.data.conversation.context_type}`);
        }

        if (response.data.initial_message) {
          this.testResults.welcomeMessage = true;
          TestUtils.logSuccess('Welcome message generated');
          TestUtils.logInfo(`Message length: ${response.data.initial_message.length} characters`);
        }

        if (response.data.suggestions && response.data.suggestions.length > 0) {
          this.testResults.suggestedQuestions = true;
          TestUtils.logSuccess(`Generated ${response.data.suggestions.length} suggested questions`);
        }
      }

      await TestUtils.delay(1000);
    } catch (error) {
      if (error.response?.status === 404) {
        TestUtils.logWarning('No assessment found - auto-initialization skipped');
      } else {
        TestUtils.logError(`Auto-initialization test failed: ${error.response?.data?.error || error.message}`);
      }
    }
  }

  async testSuggestedQuestions() {
    TestUtils.logStage('Test 3: Suggested Questions Endpoint');
    
    if (!this.testUser.conversationId) {
      TestUtils.logWarning('No conversation available - skipping suggested questions test');
      return;
    }

    try {
      const response = await axios.get(
        `${config.api.baseUrl}/api/chatbot/assessment/conversations/${this.testUser.conversationId}/suggestions`,
        {
          headers: {
            'Authorization': `Bearer ${this.testUser.token}`
          },
          timeout: config.api.timeout
        }
      );

      if (response.status === 200 && response.data.suggestions) {
        this.testResults.suggestedQuestions = true;
        TestUtils.logSuccess(`Suggestions endpoint working: ${response.data.suggestions.length} suggestions`);
        
        response.data.suggestions.forEach((suggestion, index) => {
          TestUtils.logInfo(`${index + 1}. ${suggestion}`);
        });
      }

      await TestUtils.delay(1000);
    } catch (error) {
      TestUtils.logError(`Suggested questions test failed: ${error.response?.data?.error || error.message}`);
    }
  }

  async testAssessmentConversation() {
    TestUtils.logStage('Test 4: Assessment Conversation Creation');
    
    if (!this.testUser.assessmentId) {
      TestUtils.logWarning('No assessment ID available - creating mock conversation');
      
      try {
        // Test with mock assessment ID
        const response = await axios.post(
          `${config.api.baseUrl}/api/chatbot/assessment/from-assessment`,
          {
            assessment_id: '12345678-1234-1234-1234-123456789012', // Mock UUID
            title: 'Test Assessment Conversation',
            auto_start_message: true
          },
          {
            headers: {
              'Authorization': `Bearer ${this.testUser.token}`,
              'Content-Type': 'application/json'
            },
            timeout: config.api.timeout
          }
        );

        if (response.status === 201) {
          this.testResults.assessmentConversation = true;
          TestUtils.logSuccess('Assessment conversation creation endpoint working');
        }
      } catch (error) {
        if (error.response?.status === 404) {
          TestUtils.logInfo('Assessment not found (expected for mock ID) - endpoint validation working');
          this.testResults.assessmentConversation = true;
        } else {
          TestUtils.logError(`Assessment conversation test failed: ${error.response?.data?.error || error.message}`);
        }
      }
      return;
    }

    try {
      const response = await axios.post(
        `${config.api.baseUrl}/api/chatbot/assessment/from-assessment`,
        {
          assessment_id: this.testUser.assessmentId,
          title: 'Test Assessment Conversation',
          auto_start_message: true
        },
        {
          headers: {
            'Authorization': `Bearer ${this.testUser.token}`,
            'Content-Type': 'application/json'
          },
          timeout: config.api.timeout
        }
      );

      if (response.status === 201 || response.status === 200) {
        this.testResults.assessmentConversation = true;
        TestUtils.logSuccess('Assessment conversation created successfully');
        
        if (!this.testUser.conversationId) {
          this.testUser.conversationId = response.data.conversation.id;
        }
      }

      await TestUtils.delay(1000);
    } catch (error) {
      TestUtils.logError(`Assessment conversation test failed: ${error.response?.data?.error || error.message}`);
    }
  }

  async testContextualResponses() {
    TestUtils.logStage('Test 5: Contextual AI Responses');
    
    if (!this.testUser.conversationId) {
      TestUtils.logWarning('No conversation available - skipping contextual responses test');
      return;
    }

    try {
      const testQuestions = [
        "What career paths would be best for my personality?",
        "How can I use my strengths in my career?",
        "What work environments suit me best?"
      ];

      for (const question of testQuestions) {
        TestUtils.logInfo(`Testing question: "${question}"`);
        
        const response = await axios.post(
          `${config.api.baseUrl}/api/chatbot/conversations/${this.testUser.conversationId}/messages`,
          {
            content: question
          },
          {
            headers: {
              'Authorization': `Bearer ${this.testUser.token}`,
              'Content-Type': 'application/json'
            },
            timeout: 30000 // Longer timeout for AI response
          }
        );

        if (response.status === 200 && response.data.assistant_message) {
          this.testResults.contextualResponses = true;
          TestUtils.logSuccess(`AI response received: ${response.data.assistant_message.content.length} characters`);
          
          // Check if response mentions assessment context
          const content = response.data.assistant_message.content.toLowerCase();
          if (content.includes('assessment') || content.includes('personality') || content.includes('strength')) {
            TestUtils.logInfo('Response appears to reference assessment context ✓');
          }
        }

        await TestUtils.delay(2000); // Wait between questions
      }

    } catch (error) {
      TestUtils.logError(`Contextual responses test failed: ${error.response?.data?.error || error.message}`);
    }
  }

  async cleanup() {
    TestUtils.logStage('Cleanup: Removing Test User');
    
    try {
      if (this.testUser.token) {
        await TestUtils.deleteUserAccount(this.testUser.token, config.api.baseUrl);
        TestUtils.logSuccess('Test user cleaned up');
      }
    } catch (error) {
      TestUtils.logWarning(`Cleanup failed: ${error.message}`);
    }
  }

  generateReport() {
    const totalDuration = Date.now() - this.startTime;
    const passedTests = Object.values(this.testResults).filter(result => result).length;
    const totalTests = Object.keys(this.testResults).length;
    
    console.log(chalk.bold.green('\n📊 PHASE 3 TEST REPORT'));
    console.log(chalk.gray('='.repeat(60)));
    
    console.log(chalk.bold(`\n⏱️  Total Test Duration: ${TestUtils.formatDuration(totalDuration)}`));
    console.log(chalk.bold(`👤 Test User: ${this.testUser.email}`));
    console.log(chalk.bold(`📈 Tests Passed: ${passedTests}/${totalTests}`));
    
    console.log(chalk.bold.yellow('\n🎯 PHASE 3 FEATURE RESULTS:'));
    
    Object.entries(this.testResults).forEach(([feature, passed]) => {
      const status = passed ? chalk.green('✅') : chalk.red('❌');
      const featureName = feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`${status} ${featureName}`);
    });
    
    if (passedTests === totalTests) {
      console.log(chalk.bold.green('\n🎉 All Phase 3 features working correctly!'));
    } else {
      console.log(chalk.bold.yellow(`\n⚠️  ${totalTests - passedTests} features need attention`));
    }
    
    console.log(chalk.gray('\n' + '='.repeat(60)));
    console.log(chalk.bold.green('✅ Phase 3 test completed!'));
  }
}

// Run the Phase 3 test
if (require.main === module) {
  const phase3Tester = new Phase3AssessmentIntegrationTest();
  phase3Tester.run().catch(error => {
    console.error(chalk.red('Phase 3 test failed:'), error);
    process.exit(1);
  });
}

module.exports = Phase3AssessmentIntegrationTest;
